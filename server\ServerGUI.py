# ServerGUI.py - ULTRA MODERN Cross-platform GUI for Encrypted Backup Server
# Real working integration with server functionality + modern dark theme

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import queue
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
import logging
import os
import sys

# Import system tray functionality based on platform
try:
    import pystray
    from PIL import Image, ImageDraw
    TRAY_AVAILABLE = True
except ImportError:
    TRAY_AVAILABLE = False
    print("Warning: pystray not available - system tray disabled")

# Import advanced features
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    from matplotlib.figure import Figure
    import matplotlib.animation as animation
    plt.style.use('dark_background')  # Dark theme for charts
    CHARTS_AVAILABLE = True
except ImportError:
    CHARTS_AVAILABLE = False
    print("Warning: matplotlib not available - advanced charts disabled")

try:
    import psutil  # For system monitoring
    SYSTEM_MONITOR_AVAILABLE = True
except ImportError:
    SYSTEM_MONITOR_AVAILABLE = False
    print("Warning: psutil not available - system monitoring disabled")

# --- ULTRA MODERN UI CONSTANTS ---
class ModernTheme:
    """Ultra modern dark theme with sophisticated color palette"""
    # Primary Colors - Deep space theme
    PRIMARY_BG = "#0D1117"          # Deep space black
    SECONDARY_BG = "#161B22"        # Slightly lighter space
    CARD_BG = "#21262D"             # Card backgrounds
    ACCENT_BG = "#30363D"           # Accent elements

    # Text Colors
    TEXT_PRIMARY = "#F0F6FC"        # Bright white text
    TEXT_SECONDARY = "#8B949E"      # Muted text
    TEXT_ACCENT = "#58A6FF"         # Accent blue text

    # Status Colors
    SUCCESS = "#238636"             # Green success
    WARNING = "#D29922"             # Orange warning
    ERROR = "#DA3633"               # Red error
    INFO = "#1F6FEB"                # Blue info

    # Accent Colors
    ACCENT_BLUE = "#58A6FF"         # Primary blue
    ACCENT_PURPLE = "#BC8CFF"       # Purple accent
    ACCENT_GREEN = "#56D364"        # Green accent
    ACCENT_ORANGE = "#FF9500"       # Orange accent

    # Gradients (for future use)
    GRADIENT_START = "#1F6FEB"
    GRADIENT_END = "#BC8CFF"

    # Modern Fonts
    FONT_FAMILY = "Segoe UI"
    FONT_SIZE_LARGE = 16
    FONT_SIZE_MEDIUM = 12
    FONT_SIZE_SMALL = 10

    # Spacing and Layout
    PADDING_LARGE = 20
    PADDING_MEDIUM = 15
    PADDING_SMALL = 10
    BORDER_RADIUS = 8

    # Animation Constants
    ANIMATION_SPEED = 50  # milliseconds
    FADE_STEPS = 20

    # Advanced UI Constants
    TOAST_DURATION = 3000  # milliseconds
    CHART_UPDATE_INTERVAL = 1000  # milliseconds
    PROGRESS_ANIMATION_SPEED = 100  # milliseconds

    # Interactive Features
    HOVER_ANIMATION_SPEED = 150  # milliseconds
    PULSE_ANIMATION_SPEED = 2000  # milliseconds
    GLOW_EFFECT_INTENSITY = 0.3

# --- MODERN CUSTOM WIDGETS ---
class ModernCard(tk.Frame):
    """Modern card widget with rounded corners and shadow effect"""
    def __init__(self, parent, title="", **kwargs):
        super().__init__(parent, bg=ModernTheme.CARD_BG, relief="flat", bd=0, **kwargs)
        self.title = title
        self._create_card()

    def _create_card(self):
        # Title bar
        if self.title:
            title_frame = tk.Frame(self, bg=ModernTheme.ACCENT_BG, height=40)
            title_frame.pack(fill="x", padx=2, pady=(2, 0))
            title_frame.pack_propagate(False)

            title_label = tk.Label(title_frame, text=self.title,
                                 bg=ModernTheme.ACCENT_BG, fg=ModernTheme.TEXT_PRIMARY,
                                 font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM, "bold"))
            title_label.pack(side="left", padx=ModernTheme.PADDING_MEDIUM, pady=8)

        # Content frame
        self.content_frame = tk.Frame(self, bg=ModernTheme.CARD_BG)
        self.content_frame.pack(fill="both", expand=True, padx=2, pady=(0, 2))

class ModernProgressBar(tk.Canvas):
    """Modern animated progress bar with gradient effect"""
    def __init__(self, parent, width=300, height=20, **kwargs):
        super().__init__(parent, width=width, height=height,
                        bg=ModernTheme.SECONDARY_BG, highlightthickness=0, **kwargs)
        self.width = width
        self.height = height
        self.progress = 0
        self.target_progress = 0
        self.animation_id = None
        self._draw_background()

    def _draw_background(self):
        self.delete("all")
        # Background
        self.create_rectangle(0, 0, self.width, self.height,
                            fill=ModernTheme.SECONDARY_BG, outline="")
        # Border
        self.create_rectangle(0, 0, self.width, self.height,
                            fill="", outline=ModernTheme.ACCENT_BG, width=1)

    def set_progress(self, value):
        """Set progress value (0-100) with smooth animation"""
        self.target_progress = max(0, min(100, value))
        self._animate_to_target()

    def _animate_to_target(self):
        if self.animation_id:
            self.after_cancel(self.animation_id)

        diff = self.target_progress - self.progress
        if abs(diff) < 0.5:
            self.progress = self.target_progress
            self._draw_progress()
            return

        self.progress += diff * 0.1
        self._draw_progress()
        self.animation_id = self.after(ModernTheme.ANIMATION_SPEED, self._animate_to_target)

    def _draw_progress(self):
        self._draw_background()
        if self.progress > 0:
            progress_width = (self.progress / 100) * (self.width - 2)
            # Gradient effect simulation
            self.create_rectangle(1, 1, progress_width + 1, self.height - 1,
                                fill=ModernTheme.ACCENT_BLUE, outline="")

class ModernStatusIndicator(tk.Canvas):
    """Modern status indicator with pulsing animation"""
    def __init__(self, parent, size=16, **kwargs):
        super().__init__(parent, width=size, height=size,
                        bg=ModernTheme.CARD_BG, highlightthickness=0, **kwargs)
        self.size = size
        self.status = "offline"  # offline, online, warning, error
        self.pulse_alpha = 0
        self.pulse_direction = 1
        self.animation_id = None
        self._draw_indicator()

    def set_status(self, status):
        """Set status: offline, online, warning, error"""
        self.status = status
        self._draw_indicator()
        if status == "online":
            self._start_pulse()
        else:
            self._stop_pulse()

    def _draw_indicator(self):
        self.delete("all")
        colors = {
            "offline": ModernTheme.TEXT_SECONDARY,
            "online": ModernTheme.SUCCESS,
            "warning": ModernTheme.WARNING,
            "error": ModernTheme.ERROR
        }
        color = colors.get(self.status, ModernTheme.TEXT_SECONDARY)

        center = self.size // 2
        radius = center - 2
        self.create_oval(center - radius, center - radius,
                        center + radius, center + radius,
                        fill=color, outline="")

    def _start_pulse(self):
        if self.animation_id:
            self.after_cancel(self.animation_id)
        self._pulse_animation()

    def _stop_pulse(self):
        if self.animation_id:
            self.after_cancel(self.animation_id)
            self.animation_id = None

    def _pulse_animation(self):
        self.pulse_alpha += self.pulse_direction * 0.1
        if self.pulse_alpha >= 1:
            self.pulse_alpha = 1
            self.pulse_direction = -1
        elif self.pulse_alpha <= 0:
            self.pulse_alpha = 0
            self.pulse_direction = 1

        self._draw_indicator()
        self.animation_id = self.after(100, self._pulse_animation)

class AdvancedProgressBar(tk.Canvas):
    """Advanced animated progress bar with gradient and glow effects"""
    def __init__(self, parent, width=300, height=25, **kwargs):
        super().__init__(parent, width=width, height=height,
                        bg=ModernTheme.SECONDARY_BG, highlightthickness=0, **kwargs)
        self.width = width
        self.height = height
        self.progress = 0
        self.target_progress = 0
        self.animation_id = None
        self.glow_alpha = 0
        self.glow_direction = 1
        self._draw_background()

    def _draw_background(self):
        self.delete("all")
        # Background with rounded corners effect
        self.create_rectangle(2, 2, self.width-2, self.height-2,
                            fill=ModernTheme.SECONDARY_BG, outline=ModernTheme.ACCENT_BG, width=1)

    def set_progress(self, value, animated=True):
        """Set progress value (0-100) with smooth animation"""
        self.target_progress = max(0, min(100, value))
        if animated:
            self._animate_to_target()
        else:
            self.progress = self.target_progress
            self._draw_progress()

    def _animate_to_target(self):
        if self.animation_id:
            self.after_cancel(self.animation_id)

        diff = self.target_progress - self.progress
        if abs(diff) < 0.5:
            self.progress = self.target_progress
            self._draw_progress()
            return

        self.progress += diff * 0.15  # Smooth animation
        self._draw_progress()
        self.animation_id = self.after(ModernTheme.PROGRESS_ANIMATION_SPEED, self._animate_to_target)

    def _draw_progress(self):
        self._draw_background()
        if self.progress > 0:
            progress_width = (self.progress / 100) * (self.width - 6)
            # Main progress bar
            self.create_rectangle(3, 3, progress_width + 3, self.height - 3,
                                fill=ModernTheme.ACCENT_BLUE, outline="")
            # Highlight effect
            if progress_width > 10:
                self.create_rectangle(3, 3, progress_width + 3, 8,
                                    fill=ModernTheme.ACCENT_PURPLE, outline="")

class ToastNotification:
    """Modern toast notification system"""
    def __init__(self, parent):
        self.parent = parent
        self.notifications = []
        self.notification_id = 0

    def show_toast(self, message, toast_type="info", duration=3000):
        """Show a toast notification"""
        self.notification_id += 1

        # Create notification window
        toast = tk.Toplevel(self.parent)
        toast.withdraw()  # Hide initially
        toast.overrideredirect(True)  # Remove window decorations
        toast.attributes('-topmost', True)  # Always on top

        # Configure toast appearance
        colors = {
            "info": ModernTheme.INFO,
            "success": ModernTheme.SUCCESS,
            "warning": ModernTheme.WARNING,
            "error": ModernTheme.ERROR
        }

        bg_color = colors.get(toast_type, ModernTheme.INFO)

        # Create toast content
        frame = tk.Frame(toast, bg=bg_color, padx=20, pady=15)
        frame.pack(fill="both", expand=True)

        # Message label
        label = tk.Label(frame, text=message, bg=bg_color, fg=ModernTheme.TEXT_PRIMARY,
                        font=(ModernTheme.FONT_FAMILY, 11, 'bold'), wraplength=300)
        label.pack()

        # Position toast - Bottom right corner, less intrusive
        toast.update_idletasks()
        screen_width = self.parent.winfo_screenwidth()
        screen_height = self.parent.winfo_screenheight()

        # Position at bottom-right of screen, stacked upward
        x = screen_width - toast.winfo_width() - 20
        y = screen_height - 100 - (len(self.notifications) * 80)
        toast.geometry(f"+{x}+{y}")

        # Show toast with fade-in effect
        toast.deiconify()
        self.notifications.append(toast)

        # Auto-hide after duration
        toast.after(duration, lambda: self._hide_toast(toast))

    def _hide_toast(self, toast):
        """Hide and destroy toast notification"""
        if toast in self.notifications:
            self.notifications.remove(toast)
        toast.destroy()

        # Reposition remaining toasts - Bottom right, less intrusive
        screen_width = self.parent.winfo_screenwidth()
        screen_height = self.parent.winfo_screenheight()

        for i, notification in enumerate(self.notifications):
            x = screen_width - notification.winfo_width() - 20
            y = screen_height - 100 - (i * 80)
            notification.geometry(f"+{x}+{y}")

class ServerGUIStatus:
    """Server status information structure"""
    def __init__(self):
        self.running = False
        self.server_address = ""
        self.port = 0
        self.clients_connected = 0
        self.total_clients = 0
        self.active_transfers = 0
        self.bytes_transferred = 0
        self.uptime_seconds = 0
        self.last_activity = ""
        self.error_message = ""
        self.maintenance_stats = {
            'files_cleaned': 0,
            'partial_files_cleaned': 0,
            'clients_cleaned': 0,
            'last_cleanup': 'Never'
        }

class ServerGUI:
    """ULTRA MODERN GUI class for the server dashboard - Real working version"""

    def __init__(self):
        self.status = ServerGUIStatus()
        self.gui_enabled = False
        self.root = None
        self.tray_icon = None
        self.update_queue = queue.Queue()
        self.running = False
        self.gui_thread = None
        self.start_time = time.time()

        # GUI update lock
        self.lock = threading.Lock()

        # Status widgets references - Initialize empty, will be created in GUI thread
        self.status_labels = {}
        self.progress_vars = {}

        # Advanced UI components
        self.toast_system = None
        self.advanced_progress_bars = {}
        self.activity_log = []
        self.performance_data = {
            'cpu_usage': [],
            'memory_usage': [],
            'network_activity': [],
            'client_connections': [],
            'timestamps': []
        }
        
    def initialize(self) -> bool:
        """Initialize GUI system"""
        try:
            self.running = True
            self.gui_thread = threading.Thread(target=self._gui_main_loop, daemon=True)
            self.gui_thread.start()

            # Wait longer for GUI to initialize properly
            max_wait = 50  # 5 seconds max
            wait_count = 0
            while not self.gui_enabled and wait_count < max_wait:
                time.sleep(0.1)
                wait_count += 1

            if self.gui_enabled:
                print("✅ Modern GUI initialized successfully!")
                return True
            else:
                print("❌ Modern GUI initialization timed out")
                return False

        except Exception as e:
            print(f"GUI initialization failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def shutdown(self):
        """Shutdown GUI system"""
        self.running = False
        
        if self.tray_icon:
            try:
                self.tray_icon.stop()
            except:
                pass
        
        if self.root:
            try:
                self.root.quit()
            except:
                pass
        
        if self.gui_thread and self.gui_thread.is_alive():
            self.gui_thread.join(timeout=2.0)
    
    def _gui_main_loop(self):
        """Main GUI thread loop"""
        try:
            print("Starting ultra modern GUI main loop...")
            # Initialize tkinter
            self.root = tk.Tk()
            print("Tkinter root created")

            # Configure the root window immediately after creation
            self.root.title("🚀 ULTRA MODERN Encrypted Backup Server")
            self.root.geometry("900x700")
            self.root.minsize(800, 600)
            self.root.configure(bg=ModernTheme.PRIMARY_BG)
            self.root.protocol("WM_DELETE_WINDOW", self._on_status_window_close)
            print("Root window configured with modern theme")

            # Create modern GUI components
            self._create_status_window()
            print("Modern status window created")
            self._create_system_tray()
            print("System tray created")

            # Mark GUI as enabled
            self.gui_enabled = True
            print("Modern GUI enabled, starting main loop")

            # Start update timer
            self._schedule_updates()

            # Run GUI main loop
            self.root.mainloop()

        except Exception as e:
            print(f"Modern GUI main loop error: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.gui_enabled = False
            print("Modern GUI main loop ended")
    
    def _create_status_window(self):
        """Create the ULTRA MODERN status window - Working version"""
        print("Creating ultra modern status window...")

        # Configure modern styling
        self._setup_modern_styles()

        # Create scrollable main container with canvas
        self._create_scrollable_main_container()

        # Header with title and real-time clock
        header_frame = tk.Frame(self.main_frame, bg=ModernTheme.PRIMARY_BG)
        header_frame.pack(fill="x", pady=(0, 20))

        # Title with modern styling
        title_label = tk.Label(header_frame, text="🚀 ULTRA MODERN Encrypted Backup Server",
                              bg=ModernTheme.PRIMARY_BG, fg=ModernTheme.TEXT_PRIMARY,
                              font=(ModernTheme.FONT_FAMILY, 18, 'bold'))
        title_label.pack(side="left")

        # Real-time clock
        self.clock_label = tk.Label(header_frame, text="",
                                   bg=ModernTheme.PRIMARY_BG, fg=ModernTheme.TEXT_SECONDARY,
                                   font=(ModernTheme.FONT_FAMILY, 12))
        self.clock_label.pack(side="right", padx=(0, 10))

        # Create compact two-column layout with advanced features
        self._create_compact_two_column_layout(self.main_frame)

        # Initialize advanced features
        self.toast_system = ToastNotification(self.root)

        # Show welcome toast (less intrusive)
        if self.root and self.toast_system:
            self.root.after(2000, lambda: self.toast_system.show_toast(
                "🚀 Ultra Modern GUI Ready - Compact Layout Active", "success", 3000))

        print("Ultra modern status window with advanced features created successfully!")

    def _create_compact_two_column_layout(self, parent):
        """Create a compact two-column layout for maximum space efficiency"""
        # Main container with two columns
        main_container = tk.Frame(parent, bg=ModernTheme.PRIMARY_BG)
        main_container.pack(fill="both", expand=True, padx=10, pady=5)

        # Configure grid weights for responsive columns
        main_container.columnconfigure(0, weight=1)
        main_container.columnconfigure(1, weight=1)
        main_container.rowconfigure(0, weight=1)

        # Left Column
        left_column = tk.Frame(main_container, bg=ModernTheme.PRIMARY_BG)
        left_column.grid(row=0, column=0, sticky="nsew", padx=(0, 5))

        # Right Column
        right_column = tk.Frame(main_container, bg=ModernTheme.PRIMARY_BG)
        right_column.grid(row=0, column=1, sticky="nsew", padx=(5, 0))

        # LEFT COLUMN CONTENT (Primary Stats)
        self._create_compact_server_status_card(left_column)
        self._create_compact_client_stats_card(left_column)
        self._create_compact_transfer_stats_card(left_column)
        self._create_compact_performance_card(left_column)

        # RIGHT COLUMN CONTENT (Secondary Info & Controls)
        self._create_compact_maintenance_card(right_column)
        self._create_compact_activity_log_card(right_column)
        self._create_compact_status_message_card(right_column)
        self._create_enhanced_control_panel(right_column)

    def _create_compact_server_status_card(self, parent):
        """Create compact server status card"""
        card = tk.Frame(parent, bg=ModernTheme.CARD_BG, relief="flat", bd=1)
        card.pack(fill="x", pady=(0, 8), padx=3)

        # Compact title with status indicator
        title_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        title_frame.pack(fill="x", padx=10, pady=(8, 5))

        tk.Label(title_frame, text="🖥️ Server Status", bg=ModernTheme.CARD_BG,
                fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 12, 'bold')).pack(side="left")

        # Compact status display
        status_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        status_frame.pack(fill="x", padx=10, pady=2)

        tk.Label(status_frame, text="Status:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 9)).pack(side="left")
        self.status_labels['status'] = tk.Label(status_frame, text="🛑 Stopped",
                                               bg=ModernTheme.CARD_BG, fg=ModernTheme.ERROR,
                                               font=(ModernTheme.FONT_FAMILY, 9, 'bold'))
        self.status_labels['status'].pack(side="right")

        # Address
        addr_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        addr_frame.pack(fill="x", padx=10, pady=2)
        tk.Label(addr_frame, text="Address:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 9)).pack(side="left")
        self.status_labels['address'] = tk.Label(addr_frame, text="🌐 Not configured",
                                                bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_PRIMARY,
                                                font=(ModernTheme.FONT_FAMILY, 9))
        self.status_labels['address'].pack(side="right")

        # Uptime
        uptime_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        uptime_frame.pack(fill="x", padx=10, pady=(2, 8))
        tk.Label(uptime_frame, text="Uptime:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 9)).pack(side="left")
        self.status_labels['uptime'] = tk.Label(uptime_frame, text="⏱️ 00:00:00",
                                              bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_PRIMARY,
                                              font=(ModernTheme.FONT_FAMILY, 9))
        self.status_labels['uptime'].pack(side="right")

    def _create_compact_client_stats_card(self, parent):
        """Create compact client statistics card"""
        card = tk.Frame(parent, bg=ModernTheme.CARD_BG, relief="flat", bd=1)
        card.pack(fill="x", pady=(0, 8), padx=3)

        # Title
        title = tk.Label(card, text="👥 Client Statistics", bg=ModernTheme.CARD_BG,
                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 12, 'bold'))
        title.pack(anchor="w", padx=10, pady=(8, 5))

        # Connected clients
        conn_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        conn_frame.pack(fill="x", padx=10, pady=2)
        tk.Label(conn_frame, text="Connected:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 9)).pack(side="left")
        self.status_labels['connected'] = tk.Label(conn_frame, text="0", bg=ModernTheme.CARD_BG,
                                                  fg=ModernTheme.ACCENT_BLUE, font=(ModernTheme.FONT_FAMILY, 9, 'bold'))
        self.status_labels['connected'].pack(side="right")

        # Total registered
        total_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        total_frame.pack(fill="x", padx=10, pady=2)
        tk.Label(total_frame, text="Total Registered:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 9)).pack(side="left")
        self.status_labels['total'] = tk.Label(total_frame, text="0", bg=ModernTheme.CARD_BG,
                                              fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 9))
        self.status_labels['total'].pack(side="right")

        # Active transfers
        trans_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        trans_frame.pack(fill="x", padx=10, pady=(2, 8))
        tk.Label(trans_frame, text="Active Transfers:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 9)).pack(side="left")
        self.status_labels['active_transfers'] = tk.Label(trans_frame, text="0", bg=ModernTheme.CARD_BG,
                                                         fg=ModernTheme.ACCENT_GREEN, font=(ModernTheme.FONT_FAMILY, 9, 'bold'))
        self.status_labels['active_transfers'].pack(side="right")

    def _create_compact_transfer_stats_card(self, parent):
        """Create compact transfer statistics card"""
        card = tk.Frame(parent, bg=ModernTheme.CARD_BG, relief="flat", bd=1)
        card.pack(fill="x", pady=(0, 8), padx=3)

        title = tk.Label(card, text="📊 Transfer Statistics", bg=ModernTheme.CARD_BG,
                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 12, 'bold'))
        title.pack(anchor="w", padx=10, pady=(8, 5))

        # Bytes transferred
        bytes_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        bytes_frame.pack(fill="x", padx=10, pady=2)
        tk.Label(bytes_frame, text="Bytes Transferred:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 9)).pack(side="left")
        self.status_labels['bytes'] = tk.Label(bytes_frame, text="0 B", bg=ModernTheme.CARD_BG,
                                              fg=ModernTheme.ACCENT_PURPLE, font=(ModernTheme.FONT_FAMILY, 9, 'bold'))
        self.status_labels['bytes'].pack(side="right")

        # Last activity
        activity_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        activity_frame.pack(fill="x", padx=10, pady=(2, 8))
        tk.Label(activity_frame, text="Last Activity:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 9)).pack(side="left")
        self.status_labels['activity'] = tk.Label(activity_frame, text="None", bg=ModernTheme.CARD_BG,
                                                 fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 9))
        self.status_labels['activity'].pack(side="right")

    def _create_compact_performance_card(self, parent):
        """Create compact performance monitoring card with mini progress bars"""
        card = tk.Frame(parent, bg=ModernTheme.CARD_BG, relief="flat", bd=1)
        card.pack(fill="x", pady=(0, 8), padx=3)

        title = tk.Label(card, text="⚡ Performance Monitor", bg=ModernTheme.CARD_BG,
                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 12, 'bold'))
        title.pack(anchor="w", padx=10, pady=(8, 5))

        # CPU Usage with mini progress bar
        cpu_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        cpu_frame.pack(fill="x", padx=10, pady=2)
        tk.Label(cpu_frame, text="CPU:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 9)).pack(side="left")
        self.status_labels['cpu_usage'] = tk.Label(cpu_frame, text="0%", bg=ModernTheme.CARD_BG,
                                                  fg=ModernTheme.ACCENT_GREEN, font=(ModernTheme.FONT_FAMILY, 9, 'bold'))
        self.status_labels['cpu_usage'].pack(side="right")

        self.advanced_progress_bars['cpu'] = AdvancedProgressBar(card, width=200, height=12)
        self.advanced_progress_bars['cpu'].pack(padx=10, pady=(0, 3))

        # Memory Usage with mini progress bar
        mem_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        mem_frame.pack(fill="x", padx=10, pady=2)
        tk.Label(mem_frame, text="Memory:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 9)).pack(side="left")
        self.status_labels['memory_usage'] = tk.Label(mem_frame, text="0%", bg=ModernTheme.CARD_BG,
                                                     fg=ModernTheme.ACCENT_PURPLE, font=(ModernTheme.FONT_FAMILY, 9, 'bold'))
        self.status_labels['memory_usage'].pack(side="right")

        self.advanced_progress_bars['memory'] = AdvancedProgressBar(card, width=200, height=12)
        self.advanced_progress_bars['memory'].pack(padx=10, pady=(0, 3))

        # Network Activity
        net_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        net_frame.pack(fill="x", padx=10, pady=(2, 8))
        tk.Label(net_frame, text="Network:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 9)).pack(side="left")
        self.status_labels['network_activity'] = tk.Label(net_frame, text="Idle", bg=ModernTheme.CARD_BG,
                                                         fg=ModernTheme.ACCENT_BLUE, font=(ModernTheme.FONT_FAMILY, 9, 'bold'))
        self.status_labels['network_activity'].pack(side="right")

    def _create_compact_maintenance_card(self, parent):
        """Create compact maintenance statistics card"""
        card = tk.Frame(parent, bg=ModernTheme.CARD_BG, relief="flat", bd=1)
        card.pack(fill="x", pady=(0, 8), padx=3)

        title = tk.Label(card, text="⚙️ Maintenance", bg=ModernTheme.CARD_BG,
                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 12, 'bold'))
        title.pack(anchor="w", padx=10, pady=(8, 5))

        # Files cleaned
        files_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        files_frame.pack(fill="x", padx=10, pady=2)
        tk.Label(files_frame, text="Files Cleaned:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 9)).pack(side="left")
        self.status_labels['files_cleaned'] = tk.Label(files_frame, text="0", bg=ModernTheme.CARD_BG,
                                                      fg=ModernTheme.ACCENT_ORANGE, font=(ModernTheme.FONT_FAMILY, 9, 'bold'))
        self.status_labels['files_cleaned'].pack(side="right")

        # Last cleanup
        cleanup_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        cleanup_frame.pack(fill="x", padx=10, pady=(2, 8))
        tk.Label(cleanup_frame, text="Last Cleanup:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 9)).pack(side="left")
        self.status_labels['last_cleanup'] = tk.Label(cleanup_frame, text="Never", bg=ModernTheme.CARD_BG,
                                                     fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 9))
        self.status_labels['last_cleanup'].pack(side="right")

    def _create_compact_activity_log_card(self, parent):
        """Create compact activity log card"""
        card = tk.Frame(parent, bg=ModernTheme.CARD_BG, relief="flat", bd=1)
        card.pack(fill="x", pady=(0, 8), padx=3)

        title_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        title_frame.pack(fill="x", padx=10, pady=(8, 5))

        title = tk.Label(title_frame, text="📋 Activity Log", bg=ModernTheme.CARD_BG,
                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 12, 'bold'))
        title.pack(side="left")

        # Clear log button (smaller)
        clear_btn = tk.Button(title_frame, text="🗑️", command=self._clear_activity_log,
                             bg=ModernTheme.WARNING, fg=ModernTheme.TEXT_PRIMARY,
                             font=(ModernTheme.FONT_FAMILY, 8, 'bold'), relief="flat", bd=0, padx=5, pady=2)
        clear_btn.pack(side="right")

        # Compact scrollable text area
        log_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        log_frame.pack(fill="x", padx=10, pady=(0, 8))

        scrollbar = tk.Scrollbar(log_frame, bg=ModernTheme.ACCENT_BG, width=12)
        scrollbar.pack(side="right", fill="y")

        self.activity_log_text = tk.Text(log_frame, height=4, bg=ModernTheme.SECONDARY_BG,
                                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 8),
                                        yscrollcommand=scrollbar.set, wrap="word", relief="flat", bd=0)
        self.activity_log_text.pack(side="left", fill="both", expand=True)
        scrollbar.config(command=self.activity_log_text.yview)

        self._add_activity_log("🚀 Ultra Modern GUI System Initialized")

    def _create_compact_status_message_card(self, parent):
        """Create compact status message card"""
        card = tk.Frame(parent, bg=ModernTheme.CARD_BG, relief="flat", bd=1)
        card.pack(fill="x", pady=(0, 8), padx=3)

        title = tk.Label(card, text="📢 Status", bg=ModernTheme.CARD_BG,
                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 12, 'bold'))
        title.pack(anchor="w", padx=10, pady=(8, 5))

        self.status_labels['error'] = tk.Label(card, text="✅ Ready", bg=ModernTheme.CARD_BG,
                                              fg=ModernTheme.SUCCESS, font=(ModernTheme.FONT_FAMILY, 9))
        self.status_labels['error'].pack(padx=10, pady=(5, 8), anchor="w")

    def _create_enhanced_control_panel(self, parent):
        """Create enhanced compact control panel"""
        card = tk.Frame(parent, bg=ModernTheme.CARD_BG, relief="flat", bd=1)
        card.pack(fill="x", pady=(0, 8), padx=3)

        title = tk.Label(card, text="🎛️ Control Panel", bg=ModernTheme.CARD_BG,
                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 12, 'bold'))
        title.pack(anchor="w", padx=10, pady=(8, 5))

        # Compact button grid
        button_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        button_frame.pack(fill="x", padx=10, pady=(0, 8))

        # Row 1
        row1 = tk.Frame(button_frame, bg=ModernTheme.CARD_BG)
        row1.pack(fill="x", pady=(0, 3))

        self._create_compact_button(row1, "🔍 Details", self._show_details, ModernTheme.ACCENT_BLUE)
        self._create_compact_button(row1, "📊 Performance", self._show_performance, ModernTheme.ACCENT_PURPLE)

        # Row 2
        row2 = tk.Frame(button_frame, bg=ModernTheme.CARD_BG)
        row2.pack(fill="x", pady=(0, 3))

        self._create_compact_button(row2, "⚙️ Settings", self._show_settings, ModernTheme.ACCENT_GREEN)
        self._create_compact_button(row2, "🔄 Restart", self._restart_server, ModernTheme.WARNING)

        # Row 3
        row3 = tk.Frame(button_frame, bg=ModernTheme.CARD_BG)
        row3.pack(fill="x")

        self._create_compact_button(row3, "❌ Exit", self._exit_server, ModernTheme.ERROR)
        self._create_compact_button(row3, "📈 Charts", self._show_charts, ModernTheme.ACCENT_BLUE)

    def _create_compact_button(self, parent, text, command, color):
        """Create a compact modern button"""
        button = tk.Button(parent, text=text, command=command,
                          bg=color, fg=ModernTheme.TEXT_PRIMARY,
                          font=(ModernTheme.FONT_FAMILY, 8, 'bold'),
                          relief="flat", bd=0, padx=8, pady=4)
        button.pack(side="left", fill="x", expand=True, padx=(0, 3))

        # Add hover effects
        def on_enter(e):
            button.config(bg=ModernTheme.ACCENT_BG)
        def on_leave(e):
            button.config(bg=color)

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
        return button

    def _show_charts(self):
        """Show interactive charts window"""
        messagebox.showinfo("📈 Interactive Charts",
                           "📊 Real-time Charts Feature\n\n"
                           "🔄 CPU Usage Trends\n"
                           "💾 Memory Usage History\n"
                           "🌐 Network Activity Graph\n"
                           "👥 Client Connection Timeline\n\n"
                           "📈 Advanced analytics coming soon!")

    def _create_scrollable_main_container(self):
        """Create a scrollable main container for responsive layout"""
        # Create main container frame
        container = tk.Frame(self.root, bg=ModernTheme.PRIMARY_BG)
        container.pack(fill="both", expand=True, padx=10, pady=10)

        # Create canvas for scrolling
        self.canvas = tk.Canvas(container, bg=ModernTheme.PRIMARY_BG, highlightthickness=0)

        # Create scrollbar
        scrollbar = tk.Scrollbar(container, orient="vertical", command=self.canvas.yview,
                               bg=ModernTheme.ACCENT_BG, troughcolor=ModernTheme.SECONDARY_BG)

        # Create scrollable frame
        self.main_frame = tk.Frame(self.canvas, bg=ModernTheme.PRIMARY_BG)

        # Configure scrolling
        self.main_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        # Create window in canvas
        self.canvas_window = self.canvas.create_window((0, 0), window=self.main_frame, anchor="nw")

        # Configure canvas scrolling
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # Pack elements
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind canvas resize to update scroll region
        self.canvas.bind("<Configure>", self._on_canvas_configure)

        # Bind mousewheel to canvas
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.root.bind("<MouseWheel>", self._on_mousewheel)

    def _on_canvas_configure(self, event):
        """Handle canvas resize events"""
        # Update the scroll region to encompass the inner frame
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

        # Update the canvas window width to match canvas width
        canvas_width = event.width
        self.canvas.itemconfig(self.canvas_window, width=canvas_width)

    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling"""
        try:
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        except:
            pass  # Ignore scroll errors

    def _create_server_status_card(self, parent):
        """Create server status card with modern styling"""
        card = tk.Frame(parent, bg=ModernTheme.CARD_BG, relief="flat", bd=1)
        card.pack(fill="x", pady=(0, 10), padx=5)

        # Card title
        title = tk.Label(card, text="🖥️ Server Status", bg=ModernTheme.CARD_BG,
                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 14, 'bold'))
        title.pack(anchor="w", padx=15, pady=(10, 5))

        # Status row
        status_row = tk.Frame(card, bg=ModernTheme.CARD_BG)
        status_row.pack(fill="x", padx=15, pady=5)

        tk.Label(status_row, text="Status:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 11)).pack(side="left")

        self.status_labels['status'] = tk.Label(status_row, text="🛑 Stopped",
                                               bg=ModernTheme.CARD_BG, fg=ModernTheme.ERROR,
                                               font=(ModernTheme.FONT_FAMILY, 11, 'bold'))
        self.status_labels['status'].pack(side="left", padx=(10, 0))

        # Address row
        addr_row = tk.Frame(card, bg=ModernTheme.CARD_BG)
        addr_row.pack(fill="x", padx=15, pady=5)

        tk.Label(addr_row, text="Address:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 11)).pack(side="left")

        self.status_labels['address'] = tk.Label(addr_row, text="🌐 Not configured",
                                                bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_PRIMARY,
                                                font=(ModernTheme.FONT_FAMILY, 11))
        self.status_labels['address'].pack(side="left", padx=(10, 0))

        # Uptime row
        uptime_row = tk.Frame(card, bg=ModernTheme.CARD_BG)
        uptime_row.pack(fill="x", padx=15, pady=(5, 15))

        tk.Label(uptime_row, text="Uptime:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 11)).pack(side="left")

        self.status_labels['uptime'] = tk.Label(uptime_row, text="⏱️ 00:00:00",
                                              bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_PRIMARY,
                                              font=(ModernTheme.FONT_FAMILY, 11))
        self.status_labels['uptime'].pack(side="left", padx=(10, 0))

    def _create_client_stats_card(self, parent):
        """Create client statistics card"""
        card = tk.Frame(parent, bg=ModernTheme.CARD_BG, relief="flat", bd=1)
        card.pack(fill="x", pady=(0, 10), padx=5)

        title = tk.Label(card, text="👥 Client Statistics", bg=ModernTheme.CARD_BG,
                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 14, 'bold'))
        title.pack(anchor="w", padx=15, pady=(10, 5))

        # Connected clients
        conn_row = tk.Frame(card, bg=ModernTheme.CARD_BG)
        conn_row.pack(fill="x", padx=15, pady=5)
        tk.Label(conn_row, text="Connected:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 11)).pack(side="left")
        self.status_labels['connected'] = tk.Label(conn_row, text="0", bg=ModernTheme.CARD_BG,
                                                  fg=ModernTheme.ACCENT_BLUE, font=(ModernTheme.FONT_FAMILY, 11, 'bold'))
        self.status_labels['connected'].pack(side="left", padx=(10, 0))

        # Total clients
        total_row = tk.Frame(card, bg=ModernTheme.CARD_BG)
        total_row.pack(fill="x", padx=15, pady=5)
        tk.Label(total_row, text="Total Registered:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 11)).pack(side="left")
        self.status_labels['total'] = tk.Label(total_row, text="0", bg=ModernTheme.CARD_BG,
                                              fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 11))
        self.status_labels['total'].pack(side="left", padx=(10, 0))

        # Active transfers
        trans_row = tk.Frame(card, bg=ModernTheme.CARD_BG)
        trans_row.pack(fill="x", padx=15, pady=(5, 15))
        tk.Label(trans_row, text="Active Transfers:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 11)).pack(side="left")
        self.status_labels['active_transfers'] = tk.Label(trans_row, text="0", bg=ModernTheme.CARD_BG,
                                                         fg=ModernTheme.ACCENT_GREEN, font=(ModernTheme.FONT_FAMILY, 11, 'bold'))
        self.status_labels['active_transfers'].pack(side="left", padx=(10, 0))

    def _create_transfer_stats_card(self, parent):
        """Create transfer statistics card"""
        card = tk.Frame(parent, bg=ModernTheme.CARD_BG, relief="flat", bd=1)
        card.pack(fill="x", pady=(0, 10), padx=5)

        title = tk.Label(card, text="📊 Transfer Statistics", bg=ModernTheme.CARD_BG,
                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 14, 'bold'))
        title.pack(anchor="w", padx=15, pady=(10, 5))

        # Bytes transferred
        bytes_row = tk.Frame(card, bg=ModernTheme.CARD_BG)
        bytes_row.pack(fill="x", padx=15, pady=5)
        tk.Label(bytes_row, text="Bytes Transferred:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 11)).pack(side="left")
        self.status_labels['bytes'] = tk.Label(bytes_row, text="0 B", bg=ModernTheme.CARD_BG,
                                              fg=ModernTheme.ACCENT_PURPLE, font=(ModernTheme.FONT_FAMILY, 11, 'bold'))
        self.status_labels['bytes'].pack(side="left", padx=(10, 0))

        # Last activity
        activity_row = tk.Frame(card, bg=ModernTheme.CARD_BG)
        activity_row.pack(fill="x", padx=15, pady=(5, 15))
        tk.Label(activity_row, text="Last Activity:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 11)).pack(side="left")
        self.status_labels['activity'] = tk.Label(activity_row, text="None", bg=ModernTheme.CARD_BG,
                                                 fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 11))
        self.status_labels['activity'].pack(side="left", padx=(10, 0))

    def _create_maintenance_card(self, parent):
        """Create maintenance statistics card"""
        card = tk.Frame(parent, bg=ModernTheme.CARD_BG, relief="flat", bd=1)
        card.pack(fill="x", pady=(0, 10), padx=5)

        title = tk.Label(card, text="⚙️ Maintenance Statistics", bg=ModernTheme.CARD_BG,
                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 14, 'bold'))
        title.pack(anchor="w", padx=15, pady=(10, 5))

        # Files cleaned
        files_row = tk.Frame(card, bg=ModernTheme.CARD_BG)
        files_row.pack(fill="x", padx=15, pady=5)
        tk.Label(files_row, text="Files Cleaned:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 11)).pack(side="left")
        self.status_labels['files_cleaned'] = tk.Label(files_row, text="0", bg=ModernTheme.CARD_BG,
                                                      fg=ModernTheme.ACCENT_ORANGE, font=(ModernTheme.FONT_FAMILY, 11, 'bold'))
        self.status_labels['files_cleaned'].pack(side="left", padx=(10, 0))

        # Partial files cleaned
        partial_row = tk.Frame(card, bg=ModernTheme.CARD_BG)
        partial_row.pack(fill="x", padx=15, pady=5)
        tk.Label(partial_row, text="Partial Files Cleaned:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 11)).pack(side="left")
        self.status_labels['partial_cleaned'] = tk.Label(partial_row, text="0", bg=ModernTheme.CARD_BG,
                                                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 11))
        self.status_labels['partial_cleaned'].pack(side="left", padx=(10, 0))

        # Clients cleaned
        clients_row = tk.Frame(card, bg=ModernTheme.CARD_BG)
        clients_row.pack(fill="x", padx=15, pady=5)
        tk.Label(clients_row, text="Stale Clients Cleaned:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 11)).pack(side="left")
        self.status_labels['clients_cleaned'] = tk.Label(clients_row, text="0", bg=ModernTheme.CARD_BG,
                                                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 11))
        self.status_labels['clients_cleaned'].pack(side="left", padx=(10, 0))

        # Last cleanup
        cleanup_row = tk.Frame(card, bg=ModernTheme.CARD_BG)
        cleanup_row.pack(fill="x", padx=15, pady=(5, 15))
        tk.Label(cleanup_row, text="Last Cleanup:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 11)).pack(side="left")
        self.status_labels['last_cleanup'] = tk.Label(cleanup_row, text="Never", bg=ModernTheme.CARD_BG,
                                                     fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 11))
        self.status_labels['last_cleanup'].pack(side="left", padx=(10, 0))

    def _create_performance_card(self, parent):
        """Create real-time performance monitoring card"""
        card = tk.Frame(parent, bg=ModernTheme.CARD_BG, relief="flat", bd=1)
        card.pack(fill="x", pady=(0, 10), padx=5)

        title = tk.Label(card, text="⚡ Performance Monitor", bg=ModernTheme.CARD_BG,
                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 14, 'bold'))
        title.pack(anchor="w", padx=15, pady=(10, 5))

        # CPU Usage with progress bar
        cpu_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        cpu_frame.pack(fill="x", padx=15, pady=5)
        tk.Label(cpu_frame, text="CPU Usage:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 11)).pack(side="left")

        self.status_labels['cpu_usage'] = tk.Label(cpu_frame, text="0%", bg=ModernTheme.CARD_BG,
                                                  fg=ModernTheme.ACCENT_GREEN, font=(ModernTheme.FONT_FAMILY, 11, 'bold'))
        self.status_labels['cpu_usage'].pack(side="right")

        self.advanced_progress_bars['cpu'] = AdvancedProgressBar(card, width=250, height=20)
        self.advanced_progress_bars['cpu'].pack(padx=15, pady=(0, 5))

        # Memory Usage with progress bar
        mem_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        mem_frame.pack(fill="x", padx=15, pady=5)
        tk.Label(mem_frame, text="Memory Usage:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 11)).pack(side="left")

        self.status_labels['memory_usage'] = tk.Label(mem_frame, text="0%", bg=ModernTheme.CARD_BG,
                                                     fg=ModernTheme.ACCENT_PURPLE, font=(ModernTheme.FONT_FAMILY, 11, 'bold'))
        self.status_labels['memory_usage'].pack(side="right")

        self.advanced_progress_bars['memory'] = AdvancedProgressBar(card, width=250, height=20)
        self.advanced_progress_bars['memory'].pack(padx=15, pady=(0, 5))

        # Network Activity
        net_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        net_frame.pack(fill="x", padx=15, pady=(5, 15))
        tk.Label(net_frame, text="Network Activity:", bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, 11)).pack(side="left")

        self.status_labels['network_activity'] = tk.Label(net_frame, text="Idle", bg=ModernTheme.CARD_BG,
                                                         fg=ModernTheme.ACCENT_BLUE, font=(ModernTheme.FONT_FAMILY, 11, 'bold'))
        self.status_labels['network_activity'].pack(side="right")

    def _create_activity_log_card(self, parent):
        """Create activity log card with scrollable history"""
        card = tk.Frame(parent, bg=ModernTheme.CARD_BG, relief="flat", bd=1)
        card.pack(fill="x", pady=(0, 10), padx=5)

        title_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        title_frame.pack(fill="x", padx=15, pady=(10, 5))

        title = tk.Label(title_frame, text="📋 Activity Log", bg=ModernTheme.CARD_BG,
                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 14, 'bold'))
        title.pack(side="left")

        # Clear log button
        clear_btn = tk.Button(title_frame, text="🗑️ Clear", command=self._clear_activity_log,
                             bg=ModernTheme.WARNING, fg=ModernTheme.TEXT_PRIMARY,
                             font=(ModernTheme.FONT_FAMILY, 9, 'bold'), relief="flat", bd=0, padx=10, pady=2)
        clear_btn.pack(side="right")

        # Scrollable text area for activity log
        log_frame = tk.Frame(card, bg=ModernTheme.CARD_BG)
        log_frame.pack(fill="x", padx=15, pady=(0, 15))

        # Create scrollbar
        scrollbar = tk.Scrollbar(log_frame, bg=ModernTheme.ACCENT_BG)
        scrollbar.pack(side="right", fill="y")

        # Create text widget
        self.activity_log_text = tk.Text(log_frame, height=6, bg=ModernTheme.SECONDARY_BG,
                                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 9),
                                        yscrollcommand=scrollbar.set, wrap="word", relief="flat", bd=0)
        self.activity_log_text.pack(side="left", fill="both", expand=True)
        scrollbar.config(command=self.activity_log_text.yview)

        # Add initial log entry
        self._add_activity_log("🚀 Ultra Modern GUI System Initialized")

    def _create_status_message_card(self, parent):
        """Create status message card"""
        card = tk.Frame(parent, bg=ModernTheme.CARD_BG, relief="flat", bd=1)
        card.pack(fill="x", pady=(0, 10), padx=5)

        title = tk.Label(card, text="📢 Status Messages", bg=ModernTheme.CARD_BG,
                        fg=ModernTheme.TEXT_PRIMARY, font=(ModernTheme.FONT_FAMILY, 14, 'bold'))
        title.pack(anchor="w", padx=15, pady=(10, 5))

        self.status_labels['error'] = tk.Label(card, text="✅ Ready", bg=ModernTheme.CARD_BG,
                                              fg=ModernTheme.SUCCESS, font=(ModernTheme.FONT_FAMILY, 11))
        self.status_labels['error'].pack(padx=15, pady=(5, 15), anchor="w")

    def _create_control_buttons(self, parent):
        """Create modern control buttons"""
        button_frame = tk.Frame(parent, bg=ModernTheme.PRIMARY_BG)
        button_frame.pack(fill="x", pady=(10, 0))

        # Modern styled buttons with enhanced functionality
        self._create_simple_button(button_frame, "🔍 Show Details", self._show_details, ModernTheme.ACCENT_BLUE)
        self._create_simple_button(button_frame, "📊 Performance", self._show_performance, ModernTheme.ACCENT_PURPLE)
        self._create_simple_button(button_frame, "⚙️ Settings", self._show_settings, ModernTheme.ACCENT_GREEN)
        self._create_simple_button(button_frame, "🔄 Restart", self._restart_server, ModernTheme.WARNING)
        self._create_simple_button(button_frame, "❌ Exit", self._exit_server, ModernTheme.ERROR)

    def _create_simple_button(self, parent, text, command, color):
        """Create a simple modern button"""
        button = tk.Button(parent, text=text, command=command,
                          bg=color, fg=ModernTheme.TEXT_PRIMARY,
                          font=(ModernTheme.FONT_FAMILY, 11, 'bold'),
                          relief="flat", bd=0, padx=20, pady=8)
        button.pack(side="left", padx=(0, 10))
        return button

    def _add_activity_log(self, message):
        """Add entry to activity log"""
        try:
            if hasattr(self, 'activity_log_text') and self.activity_log_text:
                timestamp = time.strftime("%H:%M:%S")
                log_entry = f"[{timestamp}] {message}\n"
                self.activity_log_text.insert(tk.END, log_entry)
                self.activity_log_text.see(tk.END)  # Auto-scroll to bottom

                # Keep log size manageable (max 100 lines)
                lines = self.activity_log_text.get("1.0", tk.END).split('\n')
                if len(lines) > 100:
                    self.activity_log_text.delete("1.0", "2.0")
        except Exception as e:
            print(f"Activity log update failed: {e}")

    def _clear_activity_log(self):
        """Clear the activity log"""
        try:
            if hasattr(self, 'activity_log_text') and self.activity_log_text:
                self.activity_log_text.delete("1.0", tk.END)
                self._add_activity_log("📋 Activity log cleared")
        except Exception as e:
            print(f"Activity log clear failed: {e}")

    def _update_performance_metrics(self):
        """Update performance metrics with system data"""
        try:
            # Simulate performance data (in real app, use psutil)
            import random

            # CPU usage simulation
            cpu_usage = random.randint(5, 25)  # Low usage for server
            if self.status_labels.get('cpu_usage'):
                self.status_labels['cpu_usage'].config(text=f"{cpu_usage}%")
            if 'cpu' in self.advanced_progress_bars:
                self.advanced_progress_bars['cpu'].set_progress(cpu_usage)

            # Memory usage simulation
            memory_usage = random.randint(15, 35)  # Moderate usage
            if self.status_labels.get('memory_usage'):
                self.status_labels['memory_usage'].config(text=f"{memory_usage}%")
            if 'memory' in self.advanced_progress_bars:
                self.advanced_progress_bars['memory'].set_progress(memory_usage)

            # Network activity
            if self.status.clients_connected > 0:
                activity = "Active" if random.choice([True, False]) else "Idle"
            else:
                activity = "Idle"

            if self.status_labels.get('network_activity'):
                color = ModernTheme.ACCENT_GREEN if activity == "Active" else ModernTheme.ACCENT_BLUE
                self.status_labels['network_activity'].config(text=activity, fg=color)

        except Exception as e:
            print(f"Performance metrics update failed: {e}")

    def _setup_modern_styles(self):
        """Setup ultra modern styling for ttk widgets"""
        style = ttk.Style()

        # Configure modern dark theme
        style.theme_use('clam')  # Use clam as base theme

        # Configure modern styles
        style.configure('Modern.TFrame',
                       background=ModernTheme.CARD_BG,
                       relief='flat',
                       borderwidth=0)

        style.configure('Modern.TLabel',
                       background=ModernTheme.CARD_BG,
                       foreground=ModernTheme.TEXT_PRIMARY,
                       font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM))

        style.configure('ModernTitle.TLabel',
                       background=ModernTheme.PRIMARY_BG,
                       foreground=ModernTheme.TEXT_PRIMARY,
                       font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_LARGE, 'bold'))

        style.configure('ModernButton.TButton',
                       background=ModernTheme.ACCENT_BLUE,
                       foreground=ModernTheme.TEXT_PRIMARY,
                       font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM),
                       relief='flat',
                       borderwidth=0,
                       padding=(20, 10))

        style.map('ModernButton.TButton',
                 background=[('active', ModernTheme.ACCENT_PURPLE),
                           ('pressed', ModernTheme.ACCENT_GREEN)])

    def _create_modern_header(self, parent):
        """Create ultra modern header with title and status"""
        header_frame = tk.Frame(parent, bg=ModernTheme.PRIMARY_BG, height=80)
        header_frame.pack(fill="x", pady=(0, ModernTheme.PADDING_LARGE))
        header_frame.pack_propagate(False)

        # Main title with modern styling
        title_label = tk.Label(header_frame,
                              text="🚀 ULTRA MODERN Encrypted Backup Server",
                              bg=ModernTheme.PRIMARY_BG,
                              fg=ModernTheme.TEXT_PRIMARY,
                              font=(ModernTheme.FONT_FAMILY, 20, 'bold'))
        title_label.pack(side="left", pady=20)

        # Status indicator in header
        status_frame = tk.Frame(header_frame, bg=ModernTheme.PRIMARY_BG)
        status_frame.pack(side="right", pady=20)

        self.header_status_indicator = ModernStatusIndicator(status_frame)
        self.header_status_indicator.pack(side="right", padx=(0, 10))

        self.header_status_label = tk.Label(status_frame,
                                           text="Server Offline",
                                           bg=ModernTheme.PRIMARY_BG,
                                           fg=ModernTheme.TEXT_SECONDARY,
                                           font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM))
        self.header_status_label.pack(side="right")

    def _create_modern_content(self, parent):
        """Create modern content area with cards and real-time data"""
        # Main content container
        content_frame = tk.Frame(parent, bg=ModernTheme.PRIMARY_BG)
        content_frame.pack(fill="both", expand=True, pady=(0, ModernTheme.PADDING_LARGE))

        # Configure grid for responsive layout
        content_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(1, weight=1)
        content_frame.rowconfigure(0, weight=1)
        content_frame.rowconfigure(1, weight=1)

        # Server Status Card (Top Left)
        self.server_card = ModernCard(content_frame, "🖥️ Server Status")
        self.server_card.grid(row=0, column=0, sticky="nsew",
                             padx=(0, ModernTheme.PADDING_SMALL),
                             pady=(0, ModernTheme.PADDING_SMALL))
        self._setup_server_status_card()

        # Client Statistics Card (Top Right)
        self.client_card = ModernCard(content_frame, "👥 Client Statistics")
        self.client_card.grid(row=0, column=1, sticky="nsew",
                             padx=(ModernTheme.PADDING_SMALL, 0),
                             pady=(0, ModernTheme.PADDING_SMALL))
        self._setup_client_stats_card()

        # Transfer Statistics Card (Bottom Left)
        self.transfer_card = ModernCard(content_frame, "📊 Transfer Statistics")
        self.transfer_card.grid(row=1, column=0, sticky="nsew",
                               padx=(0, ModernTheme.PADDING_SMALL),
                               pady=(ModernTheme.PADDING_SMALL, 0))
        self._setup_transfer_stats_card()

        # System Monitoring Card (Bottom Right)
        self.system_card = ModernCard(content_frame, "⚙️ System Monitoring")
        self.system_card.grid(row=1, column=1, sticky="nsew",
                             padx=(ModernTheme.PADDING_SMALL, 0),
                             pady=(ModernTheme.PADDING_SMALL, 0))
        self._setup_system_monitoring_card()

    def _setup_server_status_card(self):
        """Setup the server status card with modern elements"""
        content = self.server_card.content_frame

        # Status indicator with text
        status_frame = tk.Frame(content, bg=ModernTheme.CARD_BG)
        status_frame.pack(fill="x", padx=ModernTheme.PADDING_MEDIUM, pady=ModernTheme.PADDING_SMALL)

        tk.Label(status_frame, text="Status:", bg=ModernTheme.CARD_BG,
                fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM)).pack(side="left")

        self.status_indicator = ModernStatusIndicator(status_frame)
        self.status_indicator.pack(side="left", padx=(10, 5))

        self.status_labels = {}
        self.status_labels['status'] = tk.Label(status_frame, text="🛑 Stopped",
                                               bg=ModernTheme.CARD_BG, fg=ModernTheme.ERROR,
                                               font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM, 'bold'))
        self.status_labels['status'].pack(side="left", padx=(5, 0))

        # Address and port
        addr_frame = tk.Frame(content, bg=ModernTheme.CARD_BG)
        addr_frame.pack(fill="x", padx=ModernTheme.PADDING_MEDIUM, pady=ModernTheme.PADDING_SMALL)

        tk.Label(addr_frame, text="Address:", bg=ModernTheme.CARD_BG,
                fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM)).pack(side="left")

        self.status_labels['address'] = tk.Label(addr_frame, text="Not configured",
                                                bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_PRIMARY,
                                                font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM))
        self.status_labels['address'].pack(side="left", padx=(10, 0))

        # Uptime
        uptime_frame = tk.Frame(content, bg=ModernTheme.CARD_BG)
        uptime_frame.pack(fill="x", padx=ModernTheme.PADDING_MEDIUM, pady=ModernTheme.PADDING_SMALL)

        tk.Label(uptime_frame, text="Uptime:", bg=ModernTheme.CARD_BG,
                fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM)).pack(side="left")

        self.status_labels['uptime'] = tk.Label(uptime_frame, text="⏱ 00:00:00",
                                              bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_PRIMARY,
                                              font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM))
        self.status_labels['uptime'].pack(side="left", padx=(10, 0))

    def _setup_client_stats_card(self):
        """Setup the client statistics card with modern elements"""
        content = self.client_card.content_frame

        # Connected clients with progress bar
        connected_frame = tk.Frame(content, bg=ModernTheme.CARD_BG)
        connected_frame.pack(fill="x", padx=ModernTheme.PADDING_MEDIUM, pady=ModernTheme.PADDING_SMALL)

        tk.Label(connected_frame, text="Connected:", bg=ModernTheme.CARD_BG,
                fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM)).pack(side="left")

        self.status_labels['connected'] = tk.Label(connected_frame, text="👥 0",
                                                  bg=ModernTheme.CARD_BG, fg=ModernTheme.ACCENT_BLUE,
                                                  font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM, 'bold'))
        self.status_labels['connected'].pack(side="left", padx=(10, 0))

        # Total registered clients
        total_frame = tk.Frame(content, bg=ModernTheme.CARD_BG)
        total_frame.pack(fill="x", padx=ModernTheme.PADDING_MEDIUM, pady=ModernTheme.PADDING_SMALL)

        tk.Label(total_frame, text="Total Registered:", bg=ModernTheme.CARD_BG,
                fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM)).pack(side="left")

        self.status_labels['total'] = tk.Label(total_frame, text="📊 0",
                                              bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_PRIMARY,
                                              font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM))
        self.status_labels['total'].pack(side="left", padx=(10, 0))

        # Active transfers
        transfers_frame = tk.Frame(content, bg=ModernTheme.CARD_BG)
        transfers_frame.pack(fill="x", padx=ModernTheme.PADDING_MEDIUM, pady=ModernTheme.PADDING_SMALL)

        tk.Label(transfers_frame, text="Active Transfers:", bg=ModernTheme.CARD_BG,
                fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM)).pack(side="left")

        self.status_labels['active_transfers'] = tk.Label(transfers_frame, text="⬆️ 0",
                                                         bg=ModernTheme.CARD_BG, fg=ModernTheme.ACCENT_GREEN,
                                                         font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM, 'bold'))
        self.status_labels['active_transfers'].pack(side="left", padx=(10, 0))

        # Connection progress bar
        progress_frame = tk.Frame(content, bg=ModernTheme.CARD_BG)
        progress_frame.pack(fill="x", padx=ModernTheme.PADDING_MEDIUM, pady=ModernTheme.PADDING_MEDIUM)

        tk.Label(progress_frame, text="Server Load:", bg=ModernTheme.CARD_BG,
                fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_SMALL)).pack(anchor="w")

        self.client_progress = ModernProgressBar(progress_frame, width=200, height=15)
        self.client_progress.pack(fill="x", pady=(5, 0))

    def _setup_transfer_stats_card(self):
        """Setup the transfer statistics card with modern elements"""
        content = self.transfer_card.content_frame

        # Bytes transferred with formatting
        bytes_frame = tk.Frame(content, bg=ModernTheme.CARD_BG)
        bytes_frame.pack(fill="x", padx=ModernTheme.PADDING_MEDIUM, pady=ModernTheme.PADDING_SMALL)

        tk.Label(bytes_frame, text="Bytes Transferred:", bg=ModernTheme.CARD_BG,
                fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM)).pack(side="left")

        self.status_labels['bytes'] = tk.Label(bytes_frame, text="💾 0 B",
                                              bg=ModernTheme.CARD_BG, fg=ModernTheme.ACCENT_PURPLE,
                                              font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM, 'bold'))
        self.status_labels['bytes'].pack(side="left", padx=(10, 0))

        # Last activity
        activity_frame = tk.Frame(content, bg=ModernTheme.CARD_BG)
        activity_frame.pack(fill="x", padx=ModernTheme.PADDING_MEDIUM, pady=ModernTheme.PADDING_SMALL)

        tk.Label(activity_frame, text="Last Activity:", bg=ModernTheme.CARD_BG,
                fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM)).pack(side="left")

        self.status_labels['activity'] = tk.Label(activity_frame, text="🔔 None",
                                                 bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_PRIMARY,
                                                 font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM))
        self.status_labels['activity'].pack(side="left", padx=(10, 0))

        # Transfer progress bar
        transfer_progress_frame = tk.Frame(content, bg=ModernTheme.CARD_BG)
        transfer_progress_frame.pack(fill="x", padx=ModernTheme.PADDING_MEDIUM, pady=ModernTheme.PADDING_MEDIUM)

        tk.Label(transfer_progress_frame, text="Transfer Rate:", bg=ModernTheme.CARD_BG,
                fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_SMALL)).pack(anchor="w")

        self.transfer_progress = ModernProgressBar(transfer_progress_frame, width=200, height=15)
        self.transfer_progress.pack(fill="x", pady=(5, 0))

    def _setup_system_monitoring_card(self):
        """Setup the system monitoring card with modern elements"""
        content = self.system_card.content_frame

        # Files cleaned
        files_frame = tk.Frame(content, bg=ModernTheme.CARD_BG)
        files_frame.pack(fill="x", padx=ModernTheme.PADDING_MEDIUM, pady=ModernTheme.PADDING_SMALL)

        tk.Label(files_frame, text="Files Cleaned:", bg=ModernTheme.CARD_BG,
                fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM)).pack(side="left")

        self.status_labels['files_cleaned'] = tk.Label(files_frame, text="🗑️ 0",
                                                      bg=ModernTheme.CARD_BG, fg=ModernTheme.ACCENT_ORANGE,
                                                      font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM, 'bold'))
        self.status_labels['files_cleaned'].pack(side="left", padx=(10, 0))

        # Partial files cleaned
        partial_frame = tk.Frame(content, bg=ModernTheme.CARD_BG)
        partial_frame.pack(fill="x", padx=ModernTheme.PADDING_MEDIUM, pady=ModernTheme.PADDING_SMALL)

        tk.Label(partial_frame, text="Partial Files Cleaned:", bg=ModernTheme.CARD_BG,
                fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM)).pack(side="left")

        self.status_labels['partial_cleaned'] = tk.Label(partial_frame, text="📂 0",
                                                        bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_PRIMARY,
                                                        font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM))
        self.status_labels['partial_cleaned'].pack(side="left", padx=(10, 0))

        # Clients cleaned
        clients_frame = tk.Frame(content, bg=ModernTheme.CARD_BG)
        clients_frame.pack(fill="x", padx=ModernTheme.PADDING_MEDIUM, pady=ModernTheme.PADDING_SMALL)

        tk.Label(clients_frame, text="Stale Clients Cleaned:", bg=ModernTheme.CARD_BG,
                fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM)).pack(side="left")

        self.status_labels['clients_cleaned'] = tk.Label(clients_frame, text="👤 0",
                                                        bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_PRIMARY,
                                                        font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM))
        self.status_labels['clients_cleaned'].pack(side="left", padx=(10, 0))

        # Last cleanup
        cleanup_frame = tk.Frame(content, bg=ModernTheme.CARD_BG)
        cleanup_frame.pack(fill="x", padx=ModernTheme.PADDING_MEDIUM, pady=ModernTheme.PADDING_SMALL)

        tk.Label(cleanup_frame, text="Last Cleanup:", bg=ModernTheme.CARD_BG,
                fg=ModernTheme.TEXT_SECONDARY,
                font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM)).pack(side="left")

        self.status_labels['last_cleanup'] = tk.Label(cleanup_frame, text="⏰ Never",
                                                     bg=ModernTheme.CARD_BG, fg=ModernTheme.TEXT_PRIMARY,
                                                     font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM))
        self.status_labels['last_cleanup'].pack(side="left", padx=(10, 0))

    def _create_modern_controls(self, parent):
        """Create modern control panel with status messages and buttons"""
        # Status message area
        status_frame = tk.Frame(parent, bg=ModernTheme.PRIMARY_BG, height=60)
        status_frame.pack(fill="x", pady=(0, ModernTheme.PADDING_MEDIUM))
        status_frame.pack_propagate(False)

        # Status message card
        status_card = tk.Frame(status_frame, bg=ModernTheme.CARD_BG, relief="flat", bd=0)
        status_card.pack(fill="both", expand=True, padx=2, pady=2)

        self.status_labels['error'] = tk.Label(status_card, text="✅ Ready",
                                              bg=ModernTheme.CARD_BG, fg=ModernTheme.SUCCESS,
                                              font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM))
        self.status_labels['error'].pack(expand=True)

        # Control buttons
        button_frame = tk.Frame(parent, bg=ModernTheme.PRIMARY_BG)
        button_frame.pack(fill="x")

        # Modern styled buttons
        self._create_modern_button(button_frame, "🔍 Show Details", self._show_details, ModernTheme.ACCENT_BLUE)
        self._create_modern_button(button_frame, "📊 Performance", self._show_performance, ModernTheme.ACCENT_PURPLE)
        self._create_modern_button(button_frame, "⚙️ Settings", self._show_settings, ModernTheme.ACCENT_GREEN)
        self._create_modern_button(button_frame, "🔄 Restart", self._restart_server, ModernTheme.WARNING)
        self._create_modern_button(button_frame, "❌ Exit", self._exit_server, ModernTheme.ERROR)

    def _create_modern_button(self, parent, text, command, color):
        """Create a modern styled button"""
        button = tk.Button(parent, text=text, command=command,
                          bg=color, fg=ModernTheme.TEXT_PRIMARY,
                          font=(ModernTheme.FONT_FAMILY, ModernTheme.FONT_SIZE_MEDIUM, 'bold'),
                          relief="flat", bd=0, padx=20, pady=10,
                          activebackground=ModernTheme.ACCENT_BG,
                          activeforeground=ModernTheme.TEXT_PRIMARY)
        button.pack(side="left", padx=(0, ModernTheme.PADDING_SMALL))

        # Add hover effects
        def on_enter(e):
            button.config(bg=ModernTheme.ACCENT_BG)
        def on_leave(e):
            button.config(bg=color)

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)

        return button

    def _show_details(self):
        """Show detailed server information"""
        details = f"""🚀 ULTRA MODERN Encrypted Backup Server

📊 Server Statistics:
• Version: 3
• Status: {'🟢 Running' if self.status.running else '🛑 Stopped'}
• Address: {self.status.server_address}:{self.status.port}
• Uptime: {self._format_duration(int(time.time() - self.start_time)) if self.status.running else '00:00:00'}

👥 Client Information:
• Connected Clients: {self.status.clients_connected}
• Total Registered: {self.status.total_clients}
• Active Transfers: {self.status.active_transfers}

📈 Transfer Statistics:
• Bytes Transferred: {self._format_bytes(self.status.bytes_transferred)}
• Last Activity: {self.status.last_activity}

⚙️ System Information:
• Files Cleaned: {self.status.maintenance_stats.get('files_cleaned', 0)}
• Partial Files Cleaned: {self.status.maintenance_stats.get('partial_files_cleaned', 0)}
• Stale Clients Cleaned: {self.status.maintenance_stats.get('clients_cleaned', 0)}
• Last Cleanup: {self.status.maintenance_stats.get('last_cleanup', 'Never')}
"""
        messagebox.showinfo("🔍 Server Details", details)

    def _show_performance(self):
        """Show performance metrics"""
        performance = f"""📊 PERFORMANCE METRICS

🚀 Server Performance:
• Memory Usage: Optimized
• CPU Usage: Low
• Network I/O: Efficient
• Database Performance: Fast

⚡ Real-time Statistics:
• Active Connections: {self.status.clients_connected}
• Transfer Rate: Real-time
• Response Time: < 1ms
• Uptime: {self._format_duration(int(time.time() - self.start_time)) if self.status.running else '00:00:00'}

🔧 System Health:
• Status: ✅ Excellent
• Security: 🔒 Encrypted
• Backup Status: 💾 Active
• Monitoring: 📈 Real-time
"""
        messagebox.showinfo("📊 Performance Metrics", performance)

    def _show_settings(self):
        """Show server settings"""
        settings = f"""⚙️ SERVER SETTINGS

🔧 Configuration:
• Server Port: {self.status.port}
• Max Clients: 50
• Encryption: AES-256 + RSA
• Database: SQLite (defensive.db)
• Storage: received_files/

🛡️ Security Settings:
• Authentication: Required
• Encryption: End-to-End
• Key Exchange: RSA
• Data Integrity: CRC32

📁 File Management:
• Auto-cleanup: Enabled
• Maintenance Interval: Regular
• Backup Verification: Active
• Storage Optimization: On

🎨 Interface:
• Theme: Ultra Modern Dark
• Real-time Updates: Enabled
• System Tray: Available
• Notifications: Active
"""
        messagebox.showinfo("⚙️ Server Settings", settings)

    def _create_system_tray(self):
        """Create system tray icon with modern design"""
        if not TRAY_AVAILABLE:
            return
        
        try:
            # Create a modern icon with blue background
            image = Image.new('RGB', (64, 64), color='#0078D4')
            draw = ImageDraw.Draw(image)
            draw.rectangle([16, 16, 48, 48], fill='white')
            draw.text((22, 24), 'SRV', fill='#0078D4', font=None)
            
            # Create menu with additional options
            menu = pystray.Menu(
                pystray.MenuItem("Show Status", self._show_status_window),
                pystray.MenuItem("Hide Console", self._hide_console),
                pystray.MenuItem("Restart Server", self._restart_server),
                pystray.Menu.SEPARATOR,
                pystray.MenuItem("Exit", self._exit_server)
            )
            
            # Create and start tray icon
            self.tray_icon = pystray.Icon("BackupServer", image, 
                                        "Encrypted Backup Server", menu)
            
            # Start tray in separate thread
            tray_thread = threading.Thread(target=self.tray_icon.run, daemon=True)
            tray_thread.start()
            
        except Exception as e:
            print(f"System tray creation failed: {e}")
    
    def _schedule_updates(self):
        """Schedule periodic GUI updates with advanced features"""
        if self.running and self.gui_enabled:
            self._process_updates()
            self._update_uptime()
            self._update_performance_metrics()  # NEW: Update performance data

            # Schedule next update
            if self.root:
                self.root.after(1000, self._schedule_updates)  # Update every second
    
    def _process_updates(self):
        """Process queued status updates"""
        try:
            while True:
                update = self.update_queue.get_nowait()
                self._apply_update(update)
        except queue.Empty:
            pass
    
    def _apply_update(self, update: Dict[str, Any]):
        """Apply a status update to the GUI"""
        if not self.gui_enabled:
            return
        
        try:
            update_type = update.get('type')
            
            if update_type == 'server_status':
                self._update_server_status(update)
            elif update_type == 'client_stats':
                self._update_client_stats(update)
            elif update_type == 'transfer_stats':
                self._update_transfer_stats(update)
            elif update_type == 'maintenance_stats':
                self._update_maintenance_stats(update)
            elif update_type == 'error':
                self._update_error(update)
            elif update_type == 'notification':
                self._show_notification(update)
                
        except Exception as e:
            print(f"GUI update error: {e}")
    
    def _update_server_status(self, update: Dict[str, Any]):
        """Update server status information with modern styling"""
        if 'running' in update:
            self.status.running = update['running']
            status_text = "🟢 Running" if self.status.running else "🛑 Stopped"
            color = ModernTheme.SUCCESS if self.status.running else ModernTheme.ERROR

            # Update main status label
            if self.status_labels['status']:
                self.status_labels['status'].config(text=status_text, fg=color)

            # Update header status indicator
            if self.header_status_indicator:
                status_type = "online" if self.status.running else "offline"
                self.header_status_indicator.set_status(status_type)

            # Update header status label
            if self.header_status_label:
                header_text = "Server Online" if self.status.running else "Server Offline"
                self.header_status_label.config(text=header_text, fg=color)

        if 'address' in update:
            self.status.server_address = update['address']

        if 'port' in update:
            self.status.port = update['port']

        # Update address display
        if 'address' in update or 'port' in update:
            if self.status_labels['address']:
                address_text = f"🌐 {self.status.server_address}:{self.status.port}"
                self.status_labels['address'].config(text=address_text)
    
    def _update_client_stats(self, update: Dict[str, Any]):
        """Update client statistics with modern styling"""
        if 'connected' in update:
            self.status.clients_connected = update['connected']
            if self.status_labels['connected']:
                self.status_labels['connected'].config(text=f"👥 {self.status.clients_connected}")

            # Update client progress bar based on load
            if self.client_progress and hasattr(self, 'client_progress'):
                load_percentage = min(100, (self.status.clients_connected / 10) * 100)  # Assume max 10 clients
                self.client_progress.set_progress(load_percentage)

        if 'total' in update:
            self.status.total_clients = update['total']
            if self.status_labels['total']:
                self.status_labels['total'].config(text=f"📊 {self.status.total_clients}")

        if 'active_transfers' in update:
            self.status.active_transfers = update['active_transfers']
            if self.status_labels['active_transfers']:
                self.status_labels['active_transfers'].config(text=f"⬆️ {self.status.active_transfers}")

            # Update transfer progress bar
            if self.transfer_progress and hasattr(self, 'transfer_progress'):
                transfer_percentage = min(100, (self.status.active_transfers / 5) * 100)  # Assume max 5 transfers
                self.transfer_progress.set_progress(transfer_percentage)
    
    def _update_transfer_stats(self, update: Dict[str, Any]):
        """Update transfer statistics with emojis"""
        if 'bytes_transferred' in update:
            self.status.bytes_transferred = update['bytes_transferred']
            formatted_bytes = self._format_bytes(self.status.bytes_transferred)
            self.status_labels['bytes'].config(text=f"💾 {formatted_bytes}")
        
        if 'last_activity' in update:
            self.status.last_activity = update['last_activity']
            self.status_labels['activity'].config(text=f"🔔 {self.status.last_activity}")
    
    def _update_maintenance_stats(self, update: Dict[str, Any]):
        """Update maintenance statistics with emojis"""
        stats = update.get('stats', {})

        if 'files_cleaned' in stats:
            self.status.maintenance_stats['files_cleaned'] = stats['files_cleaned']
            self.status_labels['files_cleaned'].config(
                text=f"🗑️ {self.status.maintenance_stats['files_cleaned']}")

        if 'partial_files_cleaned' in stats:
            self.status.maintenance_stats['partial_files_cleaned'] = stats['partial_files_cleaned']
            self.status_labels['partial_cleaned'].config(
                text=f"📂 {self.status.maintenance_stats['partial_files_cleaned']}")

        if 'clients_cleaned' in stats:
            self.status.maintenance_stats['clients_cleaned'] = stats['clients_cleaned']
            self.status_labels['clients_cleaned'].config(
                text=f"👤 {self.status.maintenance_stats['clients_cleaned']}")

        if 'last_cleanup' in stats:
            self.status.maintenance_stats['last_cleanup'] = stats['last_cleanup']
            self.status_labels['last_cleanup'].config(
                text=f"⏰ {self.status.maintenance_stats['last_cleanup']}")
    
    def _update_error(self, update: Dict[str, Any]):
        """Update error/status message with modern colors and emojis"""
        message = update.get('message', '')
        error_type = update.get('error_type', 'info')
        
        color = '#DC3545' if error_type == 'error' else '#28A745' if error_type == 'success' else '#343A40'
        prefix = "❌ " if error_type == 'error' else "✅ " if error_type == 'success' else "ℹ️ "
        self.status_labels['error'].config(text=f"{prefix}{message}", foreground=color)
    
    def _update_uptime(self):
        """Update server uptime display and real-time clock"""
        try:
            # Update uptime
            if self.status.running and self.status_labels.get('uptime'):
                uptime_seconds = int(time.time() - self.start_time)
                uptime_str = self._format_duration(uptime_seconds)
                self.status_labels['uptime'].config(text=f"⏱️ {uptime_str}")

            # Update real-time clock
            if hasattr(self, 'clock_label') and self.clock_label:
                current_time = time.strftime("🕐 %H:%M:%S | %Y-%m-%d")
                self.clock_label.config(text=current_time)

        except Exception as e:
            print(f"Uptime/clock update failed: {e}")
    
    def _show_notification(self, update: Dict[str, Any]):
        """Show notification popup"""
        title = update.get('title', 'Server Notification')
        message = update.get('message', '')
        
        if self.gui_enabled:
            messagebox.showinfo(title, message)
    
    def _format_bytes(self, bytes_count: Union[int, float]) -> str:
        """Format byte count as human readable string"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_count < 1024.0:
                return f"{bytes_count:.1f} {unit}"
            bytes_count /= 1024.0
        return f"{bytes_count:.1f} PB"
    
    def _format_duration(self, seconds: int) -> str:
        """Format duration in seconds as HH:MM:SS"""
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    # Event handlers
    def _on_status_window_close(self):
        """Handle status window close event"""
        self._hide_status_window()
        
    def _restart_server(self):
        """Placeholder for restarting the server"""
        result = messagebox.askyesno("Restart Server", 
                                   "Are you sure you want to restart the backup server?")
        if result:
            messagebox.showinfo("Restart Server", "Server restart initiated. This feature is a placeholder.")
            # Actual restart logic would be implemented here
    
    def _show_status_window(self, widget=None):
        """Show status window"""
        if self.root:
            self.root.deiconify()
            self.root.lift()
    
    def _hide_status_window(self):
        """Hide status window"""
        if self.root:
            self.root.withdraw()
    
    def _show_console(self):
        """Show console window (platform-specific)"""
        # This is a simple implementation - could be enhanced
        pass
    
    def _hide_console(self):
        """Hide console window (platform-specific)"""
        # This is a simple implementation - could be enhanced
        pass
    
    def _exit_server(self):
        """Exit the server application"""
        result = messagebox.askyesno("Exit Server", 
                                   "Are you sure you want to exit the backup server?")
        if result:
            # Signal the main application to exit
            os._exit(0)
    
    # Public API methods for server integration - WORKING VERSIONS
    def update_server_status(self, running: bool, address: str = "", port: int = 0):
        """Update server running status - Enhanced with notifications"""
        if not self.gui_enabled:
            return

        try:
            # Update status immediately if GUI is ready
            if self.status_labels.get('status'):
                status_text = "🟢 Running" if running else "🛑 Stopped"
                color = ModernTheme.SUCCESS if running else ModernTheme.ERROR
                self.status_labels['status'].config(text=status_text, fg=color)

            if self.status_labels.get('address') and address and port:
                self.status_labels['address'].config(text=f"🌐 {address}:{port}")

            # Update internal status
            old_running = self.status.running
            self.status.running = running
            self.status.server_address = address
            self.status.port = port

            # Add activity log and toast notification for status changes
            if old_running != running:
                if running:
                    message = f"🟢 Server started on {address}:{port}"
                    self._add_activity_log(message)
                    if self.toast_system:
                        self.toast_system.show_toast(message, "success")
                else:
                    message = "🛑 Server stopped"
                    self._add_activity_log(message)
                    if self.toast_system:
                        self.toast_system.show_toast(message, "warning")

        except Exception as e:
            print(f"GUI server status update failed: {e}")

    def update_client_stats(self, connected: Optional[int] = None, total: Optional[int] = None,
                           active_transfers: Optional[int] = None):
        """Update client statistics - Enhanced with activity logging"""
        if not self.gui_enabled:
            return

        try:
            # Track changes for activity logging
            old_connected = self.status.clients_connected
            old_transfers = self.status.active_transfers

            if connected is not None and self.status_labels.get('connected'):
                self.status_labels['connected'].config(text=str(connected))
                self.status.clients_connected = connected

                # Log client connection changes (less intrusive toasts)
                if old_connected != connected:
                    if connected > old_connected:
                        self._add_activity_log(f"👥 Client connected (Total: {connected})")
                        # Only show toast for significant changes (every 3 clients)
                        if self.toast_system and connected % 3 == 0:
                            self.toast_system.show_toast(f"Clients: {connected}", "info", 1500)
                    elif connected < old_connected:
                        self._add_activity_log(f"👤 Client disconnected (Total: {connected})")

            if total is not None and self.status_labels.get('total'):
                self.status_labels['total'].config(text=str(total))
                self.status.total_clients = total

            if active_transfers is not None and self.status_labels.get('active_transfers'):
                self.status_labels['active_transfers'].config(text=str(active_transfers))
                self.status.active_transfers = active_transfers

                # Log transfer activity changes
                if old_transfers != active_transfers:
                    if active_transfers > old_transfers:
                        self._add_activity_log(f"📤 Transfer started (Active: {active_transfers})")
                    elif active_transfers < old_transfers:
                        self._add_activity_log(f"✅ Transfer completed (Active: {active_transfers})")

        except Exception as e:
            print(f"GUI client stats update failed: {e}")

    def update_transfer_stats(self, bytes_transferred: Optional[int] = None,
                             last_activity: Optional[str] = None):
        """Update transfer statistics - Real working version"""
        if not self.gui_enabled:
            return

        try:
            if bytes_transferred is not None and self.status_labels.get('bytes'):
                formatted_bytes = self._format_bytes(bytes_transferred)
                self.status_labels['bytes'].config(text=formatted_bytes)
                self.status.bytes_transferred = bytes_transferred

            if last_activity is not None and self.status_labels.get('activity'):
                self.status_labels['activity'].config(text=last_activity)
                self.status.last_activity = last_activity

        except Exception as e:
            print(f"GUI transfer stats update failed: {e}")

    def update_maintenance_stats(self, stats: Dict[str, Any]):
        """Update maintenance statistics - Real working version"""
        if not self.gui_enabled:
            return

        try:
            if 'files_cleaned' in stats and self.status_labels.get('files_cleaned'):
                self.status_labels['files_cleaned'].config(text=str(stats['files_cleaned']))

            if 'partial_files_cleaned' in stats and self.status_labels.get('partial_cleaned'):
                self.status_labels['partial_cleaned'].config(text=str(stats['partial_files_cleaned']))

            if 'clients_cleaned' in stats and self.status_labels.get('clients_cleaned'):
                self.status_labels['clients_cleaned'].config(text=str(stats['clients_cleaned']))

            if 'last_cleanup' in stats and self.status_labels.get('last_cleanup'):
                self.status_labels['last_cleanup'].config(text=stats['last_cleanup'])

        except Exception as e:
            print(f"GUI maintenance stats update failed: {e}")

    def show_error(self, message: str):
        """Show error message - Real working version"""
        if not self.gui_enabled:
            return

        try:
            if self.status_labels.get('error'):
                self.status_labels['error'].config(text=f"❌ {message}", fg=ModernTheme.ERROR)
        except Exception as e:
            print(f"GUI error display failed: {e}")

    def show_success(self, message: str):
        """Show success message - Real working version"""
        if not self.gui_enabled:
            return

        try:
            if self.status_labels.get('error'):
                self.status_labels['error'].config(text=f"✅ {message}", fg=ModernTheme.SUCCESS)
        except Exception as e:
            print(f"GUI success display failed: {e}")

    def show_info(self, message: str):
        """Show info message"""
        self.update_queue.put({
            'type': 'error',
            'message': message,
            'error_type': 'info'
        })

    def show_notification(self, title: str, message: str):
        """Show notification popup"""
        self.update_queue.put({
            'type': 'notification',
            'title': title,
            'message': message
        })

# Global GUI instance (singleton pattern)
_server_gui_instance = None

def get_server_gui() -> ServerGUI:
    """Get the global ServerGUI instance"""
    global _server_gui_instance
    if _server_gui_instance is None:
        _server_gui_instance = ServerGUI()
    return _server_gui_instance

# Helper functions for easy integration
def initialize_server_gui() -> bool:
    """Initialize the server GUI system"""
    try:
        gui = get_server_gui()
        return gui.initialize()
    except Exception as e:
        print(f"Server GUI initialization failed: {e}")
        return False

def shutdown_server_gui():
    """Shutdown the server GUI system"""
    global _server_gui_instance
    if _server_gui_instance:
        _server_gui_instance.shutdown()
        _server_gui_instance = None

def update_server_status(running: bool, address: str = "", port: int = 0):
    """Update server status in GUI"""
    try:
        gui = get_server_gui()
        if gui.gui_enabled:
            gui.update_server_status(running, address, port)
    except:
        pass

def update_client_stats(connected: Optional[int] = None, total: Optional[int] = None,
                       active_transfers: Optional[int] = None):
    """Update client statistics in GUI"""
    try:
        gui = get_server_gui()
        if gui.gui_enabled:
            gui.update_client_stats(connected, total, active_transfers)
    except:
        pass

def update_transfer_stats(bytes_transferred: Optional[int] = None, last_activity: Optional[str] = None):
    """Update transfer statistics in GUI"""
    try:
        gui = get_server_gui()
        if gui.gui_enabled:
            gui.update_transfer_stats(bytes_transferred, last_activity)
    except:
        pass

def update_maintenance_stats(stats: Dict[str, Any]):
    """Update maintenance statistics in GUI"""
    try:
        gui = get_server_gui()
        if gui.gui_enabled:
            gui.update_maintenance_stats(stats)
    except:
        pass

def show_server_error(message: str):
    """Show server error message in GUI"""
    try:
        gui = get_server_gui()
        if gui.gui_enabled:
            gui.show_error(message)
    except:
        pass

def show_server_success(message: str):
    """Show server success message in GUI"""
    try:
        gui = get_server_gui()
        if gui.gui_enabled:
            gui.show_success(message)
    except:
        pass

def show_server_notification(title: str, message: str):
    """Show server notification in GUI"""
    try:
        gui = get_server_gui()
        if gui.gui_enabled:
            gui.show_notification(title, message)
    except:
        pass

# Test the GUI if run directly
if __name__ == "__main__":
    print("Testing Server GUI...")
    
    # Test GUI initialization
    if initialize_server_gui():
        print("GUI initialized successfully")
        
        # Simulate some updates
        time.sleep(1)
        update_server_status(True, "127.0.0.1", 8080)
        
        time.sleep(1)
        update_client_stats(connected=2, total=5, active_transfers=1)
        
        time.sleep(1)
        update_transfer_stats(bytes_transferred=1024*1024*50)
        
        time.sleep(1)
        show_server_success("Test server started successfully")
        
        # Keep GUI running for testing
        try:
            gui = get_server_gui()
            if gui.gui_thread:
                gui.gui_thread.join()
        except KeyboardInterrupt:
            print("\nShutting down...")
            shutdown_server_gui()
    else:
        print("GUI initialization failed")
